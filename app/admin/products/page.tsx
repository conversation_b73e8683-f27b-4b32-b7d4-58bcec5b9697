'use client';

import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import AdminLayout from '../../../components/admin/AdminLayout';
import ImageUploadWithURL from '../../../components/ImageUploadWithURL';
import SafeImage from '../../../components/SafeImage';
import { Category, Subcategory } from '../../../types/mysql-database';
import { ProductWithDetails } from '../../../types/mysql-database';

const ProductsAdmin = () => {
  const [productsList, setProductsList] = useState<ProductWithDetails[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [categorySubcategories, setCategorySubcategories] = useState<Subcategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [showJsonModal, setShowJsonModal] = useState(false);
  const [jsonInput, setJsonInput] = useState('');
  const [editingProduct, setEditingProduct] = useState<ProductWithDetails | null>(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [formData, setFormData] = useState({
    id: '',
    title: '',
    titleAr: '',
    description: '',
    descriptionAr: '',
    images: [''],
    price: 0,
    originalPrice: 0,
    available: true,
    categoryId: '',
    subcategoryId: '',
    features: [''],
    featuresAr: [''],
    specifications: [
      { nameEn: '', nameAr: '', valueEn: '', valueAr: '' }
    ],
    isActive: true,
    isFeatured: false
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        // جلب المنتجات
        const productsResponse = await fetch('/api/admin/products');
        if (productsResponse.ok) {
          const productsResult = await productsResponse.json();
          setProductsList(productsResult.data || []);
        } else {
          console.error('Failed to fetch products:', productsResponse.status);
          setProductsList([]);
        }

        // جلب الفئات
        const categoriesResponse = await fetch('/api/admin/categories');
        if (categoriesResponse.ok) {
          const categoriesResult = await categoriesResponse.json();
          setCategories(categoriesResult.data || []);
        } else {
          console.error('Failed to fetch categories:', categoriesResponse.status);
          setCategories([]);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        setProductsList([]);
        setCategories([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // تحميل الفئات الفرعية عند تغيير الفئة في النموذج
  useEffect(() => {
    const fetchSubcategories = async () => {
      if (formData.categoryId) {
        try {
          const response = await fetch('/api/admin/subcategories');
          if (response.ok) {
            const result = await response.json();
            const filteredSubcategories = (result.data || []).filter(
              (sub: Subcategory) => sub.category_id === formData.categoryId
            );
            setCategorySubcategories(filteredSubcategories);
          }
        } catch (error) {
          console.error('Error fetching subcategories:', error);
          setCategorySubcategories([]);
        }
      } else {
        setCategorySubcategories([]);
      }
    };

    fetchSubcategories();
  }, [formData.categoryId]);

  const filteredProducts = (productsList || []).filter(product => {
    const matchesCategory = selectedCategory === 'all' || product.category_id === selectedCategory;
    const matchesSearch = product.title_ar.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.id.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // التحقق من صحة الـ ID المخصص
    if (!editingProduct && formData.id.trim()) {
      const idPattern = /^[a-zA-Z0-9_-]+$/;
      if (!idPattern.test(formData.id.trim())) {
        alert('معرف المنتج يجب أن يحتوي على أحرف وأرقام وشرطات فقط (بدون مسافات)');
        return;
      }
    }

    try {
      const requestData = {
        id: formData.id.trim() || undefined, // إضافة الـ ID المخصص
        title: formData.title,
        titleAr: formData.titleAr,
        description: formData.description,
        descriptionAr: formData.descriptionAr,
        images: formData.images.filter(img => img.trim()),
        price: formData.price,
        originalPrice: formData.originalPrice || null,
        available: formData.available,
        categoryId: formData.categoryId,
        subcategoryId: formData.subcategoryId,
        features: formData.features.filter(f => f.trim()),
        featuresAr: formData.featuresAr.filter(f => f.trim()),
        specifications: formData.specifications.filter(spec =>
          spec.nameEn.trim() && spec.nameAr.trim() && spec.valueEn.trim() && spec.valueAr.trim()
        ),
        isActive: formData.isActive,
        isFeatured: formData.isFeatured
      };

      if (editingProduct) {
        // تحديث منتج موجود
        const response = await fetch(`/api/admin/products?id=${editingProduct.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData),
        });

        if (!response.ok) {
          throw new Error('Failed to update product');
        }

        const result = await response.json();
        setProductsList(prev => prev.map(product =>
          product.id === editingProduct.id ? result.data : product
        ));
      } else {
        // إضافة منتج جديد
        const response = await fetch('/api/admin/products', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData),
        });

        if (!response.ok) {
          throw new Error('Failed to create product');
        }

        const result = await response.json();
        setProductsList(prev => [...prev, result.data]);
      }

      resetForm();
    } catch (error) {
      console.error('Error saving product:', error);
      alert('حدث خطأ أثناء حفظ المنتج');
    }
  };

  const resetForm = () => {
    setFormData({
      id: '',
      title: '',
      titleAr: '',
      description: '',
      descriptionAr: '',
      images: [''],
      price: 0,
      originalPrice: 0,
      available: true,
      categoryId: '',
      subcategoryId: '',
      features: [''],
      featuresAr: [''],
      specifications: [
        { nameEn: '', nameAr: '', valueEn: '', valueAr: '' }
      ],
      isActive: true,
      isFeatured: false
    });
    setEditingProduct(null);
    setShowModal(false);
  };

  const handleEdit = (product: ProductWithDetails) => {
    setEditingProduct(product);

    // تحويل الصور من ProductImage[] إلى string[]
    const imageUrls = product.images.map(img => img.image_url);

    // تحويل المميزات من ProductFeature[] إلى string[]
    const features = product.features.map(f => f.feature_text);
    const featuresAr = product.features.map(f => f.feature_text_ar);

    // تحويل المواصفات من ProductSpecification[] إلى SpecificationItem[]
    const specifications = product.specifications.map(spec => ({
      nameEn: spec.spec_key,
      nameAr: spec.spec_key_ar,
      valueEn: spec.spec_value,
      valueAr: spec.spec_value_ar
    }));

    setFormData({
      id: product.id,
      title: product.title,
      titleAr: product.title_ar,
      description: product.description || '',
      descriptionAr: product.description_ar || '',
      images: imageUrls.length > 0 ? imageUrls : [''],
      price: product.price,
      originalPrice: product.original_price || 0,
      available: product.is_available,
      categoryId: product.category_id,
      subcategoryId: product.subcategory_id,
      features: features.length > 0 ? features : [''],
      featuresAr: featuresAr.length > 0 ? featuresAr : [''],
      specifications: specifications.length > 0 ? specifications : [
        { nameEn: '', nameAr: '', valueEn: '', valueAr: '' }
      ],
      isActive: product.is_active,
      isFeatured: product.is_featured
    });
    setShowModal(true);
  };

  const handleDelete = async (productId: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
      try {
        const response = await fetch(`/api/admin/products?id=${productId}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error('Failed to delete product');
        }

        setProductsList(prev => prev.filter(product => product.id !== productId));
      } catch (error) {
        console.error('Error deleting product:', error);
        alert('حدث خطأ أثناء حذف المنتج');
      }
    }
  };

  const toggleStatus = async (productId: string) => {
    try {
      const product = productsList.find(p => p.id === productId);
      if (product) {
        const response = await fetch(`/api/admin/products?id=${productId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            isActive: !product.is_active
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to update product status');
        }

        const result = await response.json();
        setProductsList(prev => prev.map(p =>
          p.id === productId ? result.data : p
        ));
      }
    } catch (error) {
      console.error('Error updating product status:', error);
    }
  };

  const toggleFeatured = async (productId: string) => {
    try {
      const product = productsList.find(p => p.id === productId);
      if (product) {
        const response = await fetch(`/api/admin/products?id=${productId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            isFeatured: !product.is_featured
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to update product featured status');
        }

        const result = await response.json();
        setProductsList(prev => prev.map(p =>
          p.id === productId ? result.data : p
        ));
      }
    } catch (error) {
      console.error('Error updating product featured status:', error);
    }
  };

  const getCategoryName = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category?.name_ar || categoryId;
  };

  const [allSubcategories, setAllSubcategories] = useState<Subcategory[]>([]);

  // تحميل جميع الفئات الفرعية مرة واحدة
  useEffect(() => {
    const fetchAllSubcategories = async () => {
      try {
        const response = await fetch('/api/admin/subcategories');
        if (response.ok) {
          const result = await response.json();
          setAllSubcategories(result.data || []);
        }
      } catch (error) {
        console.error('Error fetching all subcategories:', error);
      }
    };

    fetchAllSubcategories();
  }, []);

  const getSubcategoryName = (subcategoryId: string) => {
    const subcategory = allSubcategories.find(sub => sub.id === subcategoryId);
    return subcategory?.name_ar || subcategoryId;
  };



  const addFeatureField = () => {
    setFormData(prev => ({
      ...prev,
      features: [...prev.features, ''],
      featuresAr: [...prev.featuresAr, '']
    }));
  };

  const removeFeatureField = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index),
      featuresAr: prev.featuresAr.filter((_, i) => i !== index)
    }));
  };

  const updateFeatureField = (index: number, value: string, isArabic: boolean = false) => {
    setFormData(prev => ({
      ...prev,
      [isArabic ? 'featuresAr' : 'features']: prev[isArabic ? 'featuresAr' : 'features'].map((feature, i) => i === index ? value : feature)
    }));
  };

  // دالة إضافة مواصفة جديدة
  const addSpecificationField = () => {
    setFormData(prev => ({
      ...prev,
      specifications: [...prev.specifications, { nameEn: '', nameAr: '', valueEn: '', valueAr: '' }]
    }));
  };

  // دالة حذف مواصفة
  const removeSpecificationField = (index: number) => {
    setFormData(prev => ({
      ...prev,
      specifications: prev.specifications.filter((_, i) => i !== index)
    }));
  };

  // دالة تحديث مواصفة
  const updateSpecificationField = (index: number, field: 'nameEn' | 'nameAr' | 'valueEn' | 'valueAr', value: string) => {
    setFormData(prev => ({
      ...prev,
      specifications: prev.specifications.map((spec, i) =>
        i === index ? { ...spec, [field]: value } : spec
      )
    }));
  };

  // دالة معالجة JSON وإضافة المنتج أو المنتجات
  const handleJsonSubmit = async () => {
    try {
      // تحليل JSON
      const jsonData = JSON.parse(jsonInput);

      // التحقق من نوع البيانات (منتج واحد أو مصفوفة من المنتجات)
      const isMultipleProducts = Array.isArray(jsonData);

      if (isMultipleProducts) {
        // التحقق من صحة مصفوفة المنتجات
        if (jsonData.length === 0) {
          alert('يجب أن تحتوي المصفوفة على منتج واحد على الأقل');
          return;
        }

        // التحقق من وجود الحقول المطلوبة لكل منتج
        const requiredFields = ['title', 'titleAr', 'description', 'descriptionAr', 'price', 'categoryId', 'subcategoryId'];

        for (let i = 0; i < jsonData.length; i++) {
          const product = jsonData[i];
          const missingFields = requiredFields.filter(field => !product[field]);

          if (missingFields.length > 0) {
            alert(`المنتج رقم ${i + 1}: الحقول التالية مطلوبة: ${missingFields.join(', ')}`);
            return;
          }

          // التحقق من صحة الـ ID المخصص إذا تم توفيره
          if (product.id && product.id.trim()) {
            const idPattern = /^[a-zA-Z0-9_-]+$/;
            if (!idPattern.test(product.id.trim())) {
              alert(`المنتج رقم ${i + 1}: معرف المنتج يجب أن يحتوي على أحرف وأرقام وشرطات فقط (بدون مسافات)`);
              return;
            }
          }
        }

        // تحضير البيانات للإرسال (مصفوفة من المنتجات)
        const requestData = jsonData.map(product => ({
          id: product.id?.trim() || undefined,
          title: product.title,
          titleAr: product.titleAr,
          description: product.description,
          descriptionAr: product.descriptionAr,
          images: product.images || [],
          price: parseFloat(product.price) || 0,
          originalPrice: product.originalPrice ? parseFloat(product.originalPrice) : null,
          available: product.available !== undefined ? product.available : true,
          categoryId: product.categoryId,
          subcategoryId: product.subcategoryId,
          features: product.features || [],
          featuresAr: product.featuresAr || [],
          specifications: product.specifications || [],
          isActive: product.isActive !== undefined ? product.isActive : true,
          isFeatured: product.isFeatured !== undefined ? product.isFeatured : false
        }));

        // إرسال البيانات
        const response = await fetch('/api/admin/products', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.messageAr || 'Failed to create products');
        }

        const result = await response.json();
        setProductsList(prev => [...prev, ...result.data]);

        // إعادة تعيين المودال
        setJsonInput('');
        setShowJsonModal(false);
        alert(`تم إضافة ${result.data.length} منتج بنجاح!`);

      } else {
        // معالجة منتج واحد (الكود الأصلي)
        const requiredFields = ['title', 'titleAr', 'description', 'descriptionAr', 'price', 'categoryId', 'subcategoryId'];
        const missingFields = requiredFields.filter(field => !jsonData[field]);

        if (missingFields.length > 0) {
          alert(`الحقول التالية مطلوبة: ${missingFields.join(', ')}`);
          return;
        }

        // التحقق من صحة الـ ID المخصص إذا تم توفيره
        if (jsonData.id && jsonData.id.trim()) {
          const idPattern = /^[a-zA-Z0-9_-]+$/;
          if (!idPattern.test(jsonData.id.trim())) {
            alert('معرف المنتج يجب أن يحتوي على أحرف وأرقام وشرطات فقط (بدون مسافات)');
            return;
          }
        }

        // تحضير البيانات للإرسال
        const requestData = {
          id: jsonData.id?.trim() || undefined,
          title: jsonData.title,
          titleAr: jsonData.titleAr,
          description: jsonData.description,
          descriptionAr: jsonData.descriptionAr,
          images: jsonData.images || [],
          price: parseFloat(jsonData.price) || 0,
          originalPrice: jsonData.originalPrice ? parseFloat(jsonData.originalPrice) : null,
          available: jsonData.available !== undefined ? jsonData.available : true,
          categoryId: jsonData.categoryId,
          subcategoryId: jsonData.subcategoryId,
          features: jsonData.features || [],
          featuresAr: jsonData.featuresAr || [],
          specifications: jsonData.specifications || [],
          isActive: jsonData.isActive !== undefined ? jsonData.isActive : true,
          isFeatured: jsonData.isFeatured !== undefined ? jsonData.isFeatured : false
        };

        // إرسال البيانات
        const response = await fetch('/api/admin/products', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.messageAr || 'Failed to create product');
        }

        const result = await response.json();
        setProductsList(prev => [...prev, result.data]);

        // إعادة تعيين المودال
        setJsonInput('');
        setShowJsonModal(false);
        alert('تم إضافة المنتج بنجاح!');
      }

    } catch (error) {
      console.error('Error processing JSON:', error);
      if (error instanceof SyntaxError) {
        alert('خطأ في تنسيق JSON. تأكد من صحة البيانات المدخلة.');
      } else {
        alert(`حدث خطأ: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
      }
    }
  };

  return (
    <>
      <Head>
        <title>إدارة المنتجات - لوحة التحكم</title>
        <meta name="description" content="إدارة المنتجات" />
      </Head>

      <AdminLayout title="المنتجات">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-800">المنتجات</h1>
              <p className="text-gray-600">إدارة جميع المنتجات في المتجر</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={() => setShowJsonModal(true)}
                className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl flex items-center transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                <i className="ri-code-line text-lg ml-2"></i>
                إضافة JSON
              </button>
              <button
                onClick={() => setShowModal(true)}
                className="bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-white px-6 py-3 rounded-xl flex items-center transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                <i className="ri-add-line text-lg ml-2"></i>
                إضافة منتج جديد
              </button>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">البحث:</label>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="ابحث بالاسم أو ID المنتج..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الفئة:</label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">جميع الفئات</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>{category.name_ar}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-600">جاري تحميل المنتجات...</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProducts.map((product) => (
              <div key={product.id} className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                <SafeImage
                  src={product.images[0]?.image_url || '/api/placeholder?width=400&height=300&text=لا توجد صورة'}
                  alt={product.title_ar}
                  width={400}
                  height={300}
                  className="w-full h-48 object-cover"
                  fallbackText="لا توجد صورة"
                />
                <div className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="text-lg font-semibold text-gray-800 line-clamp-2">{product.title_ar}</h3>
                        <span className="px-2 py-1 text-xs font-mono bg-blue-100 text-blue-800 rounded border">
                          {product.id}
                        </span>
                      </div>
                      <p className="text-sm text-gray-500 mb-2">{getCategoryName(product.category_id)} - {getSubcategoryName(product.subcategory_id)}</p>
                    </div>
                    <div className="flex flex-col items-end space-y-1">
                      {product.is_featured && (
                        <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                          مميز
                        </span>
                      )}
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        product.is_active
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {product.is_active ? 'نشط' : 'غير نشط'}
                      </span>
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 mb-4 line-clamp-2">{product.description_ar}</p>

                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <span className="text-lg font-bold text-blue-600">{product.price} ر.س</span>
                      {product.original_price && product.original_price > product.price && (
                        <span className="text-sm text-gray-500 line-through">{product.original_price} ر.س</span>
                      )}
                    </div>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      product.is_available
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {product.is_available ? 'متوفر' : 'غير متوفر'}
                    </span>
                  </div>

                  <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <button
                        onClick={() => handleEdit(product)}
                        className="text-blue-600 hover:text-blue-700 p-2 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                        title="تعديل"
                      >
                        <i className="ri-edit-line text-lg"></i>
                      </button>
                      <button
                        onClick={() => toggleFeatured(product.id)}
                        className={`p-2 rounded-lg transition-colors duration-200 ${
                          product.is_featured
                            ? 'text-yellow-600 hover:text-yellow-700 hover:bg-yellow-50'
                            : 'text-gray-400 hover:text-yellow-600 hover:bg-yellow-50'
                        }`}
                        title={product.is_featured ? 'إلغاء التمييز' : 'جعل مميز'}
                      >
                        <i className={`ri-star-${product.is_featured ? 'fill' : 'line'} text-lg`}></i>
                      </button>
                      <button
                        onClick={() => toggleStatus(product.id)}
                        className={`p-2 rounded-lg transition-colors duration-200 ${
                          product.is_active
                            ? 'text-red-600 hover:text-red-700 hover:bg-red-50'
                            : 'text-green-600 hover:text-green-700 hover:bg-green-50'
                        }`}
                        title={product.is_active ? 'إلغاء التفعيل' : 'تفعيل'}
                      >
                        <i className={`ri-${product.is_active ? 'eye-off' : 'eye'}-line text-lg`}></i>
                      </button>
                      <button
                        onClick={() => handleDelete(product.id)}
                        className="text-red-600 hover:text-red-700 p-2 hover:bg-red-50 rounded-lg transition-colors duration-200"
                        title="حذف"
                      >
                        <i className="ri-delete-bin-line text-lg"></i>
                      </button>
                    </div>
                    <div className="text-xs text-gray-400">
                      {new Date(product.updated_at).toLocaleDateString('ar-SA')}
                    </div>
                  </div>
                </div>
              </div>
            ))}
            </div>
          )}

          {!loading && filteredProducts.length === 0 && (
            <div className="text-center py-12">
              <i className="ri-product-hunt-line text-6xl text-gray-400 mb-4"></i>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد منتجات</h3>
              <p className="text-gray-600 mb-4">ابدأ بإضافة منتج جديد</p>
              <button
                onClick={() => setShowModal(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg inline-flex items-center transition-colors duration-200"
              >
                <i className="ri-add-line text-lg ml-2"></i>
                إضافة منتج جديد
              </button>
            </div>
          )}
        </div>

        {/* Modal */}
        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
            <div className="bg-white rounded-3xl max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl border border-gray-200">
              <div className="p-8">
                <div className="flex items-center justify-between mb-8">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center ml-4">
                      <i className={`ri-${editingProduct ? 'edit' : 'add'}-line text-white text-xl`}></i>
                    </div>
                    <h2 className="text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                      {editingProduct ? 'تعديل المنتج' : 'إضافة منتج جديد'}
                    </h2>
                  </div>
                  <button
                    onClick={resetForm}
                    className="text-gray-400 hover:text-gray-600 p-3 rounded-xl hover:bg-gray-100 transition-colors duration-200"
                  >
                    <i className="ri-close-line text-xl"></i>
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* ID Field */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div>
                      <label className="block text-sm font-medium text-blue-700 mb-2">
                        معرف المنتج (ID) {!editingProduct && '*'}
                      </label>
                      <input
                        type="text"
                        required={!editingProduct}
                        disabled={editingProduct !== null}
                        value={formData.id}
                        onChange={(e) => {
                          const value = e.target.value;
                          // السماح فقط بالأحرف والأرقام والشرطات
                          const sanitizedValue = value.replace(/[^a-zA-Z0-9_-]/g, '');
                          setFormData({...formData, id: sanitizedValue});
                        }}
                        className={`w-full px-4 py-3 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                          editingProduct ? 'bg-gray-100 text-gray-600 cursor-not-allowed' : 'bg-white'
                        }`}
                        placeholder={editingProduct ? "لا يمكن تعديل معرف المنتج" : "أدخل معرف المنتج (مثل: PROD-001)"}
                        maxLength={50}
                      />
                      <p className="text-xs text-blue-600 mt-1">
                        {editingProduct
                          ? "لا يمكن تعديل معرف المنتج بعد إنشائه"
                          : "يجب أن يكون معرف المنتج فريداً ولا يحتوي على مسافات"
                        }
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Basic Info */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-gray-800 border-b pb-2">المعلومات الأساسية</h3>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          اسم المنتج بالعربية *
                        </label>
                        <input
                          type="text"
                          required
                          value={formData.titleAr}
                          onChange={(e) => setFormData({...formData, titleAr: e.target.value})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="أدخل اسم المنتج بالعربية"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          اسم المنتج بالإنجليزية *
                        </label>
                        <input
                          type="text"
                          required
                          value={formData.title}
                          onChange={(e) => setFormData({...formData, title: e.target.value})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Enter product name in English"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          الوصف بالعربية *
                        </label>
                        <textarea
                          required
                          value={formData.descriptionAr}
                          onChange={(e) => setFormData({...formData, descriptionAr: e.target.value})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={4}
                          placeholder="أدخل وصف المنتج بالعربية"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          الوصف بالإنجليزية *
                        </label>
                        <textarea
                          required
                          value={formData.description}
                          onChange={(e) => setFormData({...formData, description: e.target.value})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={4}
                          placeholder="Enter product description in English"
                        />
                      </div>
                    </div>

                    {/* Category & Pricing */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-gray-800 border-b pb-2">الفئة والأسعار</h3>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          الفئة الرئيسية *
                        </label>
                        <select
                          required
                          value={formData.categoryId}
                          onChange={(e) => setFormData({...formData, categoryId: e.target.value, subcategoryId: ''})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="">اختر الفئة الرئيسية</option>
                          {categories.map((category) => (
                            <option key={category.id} value={category.id}>{category.name_ar}</option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          الفئة الفرعية *
                        </label>
                        <select
                          required
                          value={formData.subcategoryId}
                          onChange={(e) => setFormData({...formData, subcategoryId: e.target.value})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          disabled={!formData.categoryId}
                        >
                          <option value="">اختر الفئة الفرعية</option>
                          {categorySubcategories.map((subcategory) => (
                            <option key={subcategory.id} value={subcategory.id}>
                              {subcategory.name_ar}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            السعر *
                          </label>
                          <input
                            type="number"
                            required
                            min="0"
                            step="0.01"
                            value={formData.price}
                            onChange={(e) => setFormData({...formData, price: parseFloat(e.target.value) || 0})}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="0.00"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            السعر الأصلي
                          </label>
                          <input
                            type="number"
                            min="0"
                            step="0.01"
                            value={formData.originalPrice}
                            onChange={(e) => setFormData({...formData, originalPrice: parseFloat(e.target.value) || 0})}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="0.00"
                          />
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="available"
                            checked={formData.available}
                            onChange={(e) => setFormData({...formData, available: e.target.checked})}
                            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                          />
                          <label htmlFor="available" className="mr-2 text-sm font-medium text-gray-700">
                            متوفر للبيع
                          </label>
                        </div>

                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="isActive"
                            checked={formData.isActive}
                            onChange={(e) => setFormData({...formData, isActive: e.target.checked})}
                            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                          />
                          <label htmlFor="isActive" className="mr-2 text-sm font-medium text-gray-700">
                            منتج نشط
                          </label>
                        </div>

                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="isFeatured"
                            checked={formData.isFeatured}
                            onChange={(e) => setFormData({...formData, isFeatured: e.target.checked})}
                            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                          />
                          <label htmlFor="isFeatured" className="mr-2 text-sm font-medium text-gray-700">
                            منتج مميز
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Images */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 border-b pb-2 mb-4">صور المنتج</h3>
                    <ImageUploadWithURL
                      onImagesUploaded={(images: string[]) => {
                        const validImages = images.filter(img => img && img.trim());
                        setFormData({...formData, images: validImages});
                      }}
                      multiple={true}
                      maxFiles={5}
                      currentImages={formData.images.filter(img => img && img.trim())}
                      label="صور المنتج (حد أقصى 5 صور)"
                    />
                  </div>

                  {/* Features */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 border-b pb-2 mb-4">المميزات</h3>
                    <div className="space-y-3">
                      {formData.featuresAr.map((feature, index) => (
                        <div key={index} className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <input
                              type="text"
                              value={feature}
                              onChange={(e) => updateFeatureField(index, e.target.value, true)}
                              className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="ميزة بالعربية"
                            />
                            {formData.featuresAr.length > 1 && (
                              <button
                                type="button"
                                onClick={() => removeFeatureField(index)}
                                className="text-red-600 hover:text-red-700 p-2"
                              >
                                <i className="ri-delete-bin-line text-lg"></i>
                              </button>
                            )}
                          </div>
                          <input
                            type="text"
                            value={formData.features[index] || ''}
                            onChange={(e) => updateFeatureField(index, e.target.value, false)}
                            className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Feature in English"
                          />
                        </div>
                      ))}
                      <button
                        type="button"
                        onClick={addFeatureField}
                        className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center"
                      >
                        <i className="ri-add-line text-lg ml-1"></i>
                        إضافة ميزة أخرى
                      </button>
                    </div>
                  </div>

                  {/* Specifications */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 border-b pb-2 mb-4">المواصفات</h3>
                    <div className="space-y-4">
                      {formData.specifications.map((spec, index) => (
                        <div key={index} className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            {/* اسم المواصفة بالعربية */}
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                اسم المواصفة بالعربية
                              </label>
                              <input
                                type="text"
                                value={spec.nameAr}
                                onChange={(e) => updateSpecificationField(index, 'nameAr', e.target.value)}
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="مثل: المادة، الحجم، اللون"
                              />
                            </div>

                            {/* اسم المواصفة بالإنجليزية */}
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                اسم المواصفة بالإنجليزية
                              </label>
                              <input
                                type="text"
                                value={spec.nameEn}
                                onChange={(e) => updateSpecificationField(index, 'nameEn', e.target.value)}
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="e.g: Material, Size, Color"
                              />
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            {/* قيمة المواصفة بالعربية */}
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                قيمة المواصفة بالعربية
                              </label>
                              <input
                                type="text"
                                value={spec.valueAr}
                                onChange={(e) => updateSpecificationField(index, 'valueAr', e.target.value)}
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="مثل: فولاذ مقاوم للصدأ، كبير، أحمر"
                              />
                            </div>

                            {/* قيمة المواصفة بالإنجليزية */}
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                قيمة المواصفة بالإنجليزية
                              </label>
                              <input
                                type="text"
                                value={spec.valueEn}
                                onChange={(e) => updateSpecificationField(index, 'valueEn', e.target.value)}
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="e.g: Stainless Steel, Large, Red"
                              />
                            </div>
                          </div>

                          {/* زر الحذف */}
                          {formData.specifications.length > 1 && (
                            <div className="flex justify-end">
                              <button
                                type="button"
                                onClick={() => removeSpecificationField(index)}
                                className="text-red-600 hover:text-red-700 p-2 hover:bg-red-50 rounded-lg transition-colors duration-200"
                                title="حذف المواصفة"
                              >
                                <i className="ri-delete-bin-line text-lg"></i>
                              </button>
                            </div>
                          )}
                        </div>
                      ))}

                      {/* زر إضافة مواصفة جديدة */}
                      <button
                        type="button"
                        onClick={addSpecificationField}
                        className="w-full border-2 border-dashed border-gray-300 hover:border-blue-500 text-gray-600 hover:text-blue-600 py-4 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center"
                      >
                        <i className="ri-add-line text-lg ml-2"></i>
                        إضافة مواصفة جديدة
                      </button>
                    </div>
                  </div>

                  <div className="flex space-x-4 space-x-reverse pt-8 border-t border-gray-200">
                    <button
                      type="submit"
                      className="flex-1 bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-white py-4 px-6 rounded-xl font-bold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                    >
                      {editingProduct ? 'تحديث المنتج' : 'إضافة المنتج'}
                    </button>
                    <button
                      type="button"
                      onClick={resetForm}
                      className="flex-1 bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 text-gray-800 py-4 px-6 rounded-xl font-bold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                    >
                      إلغاء
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}

        {/* JSON Modal */}
        {showJsonModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
            <div className="bg-white rounded-3xl max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl border border-gray-200">
              <div className="p-8">
                <div className="flex items-center justify-between mb-8">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center ml-4">
                      <i className="ri-code-line text-white text-xl"></i>
                    </div>
                    <h2 className="text-2xl font-bold bg-gradient-to-r from-green-500 to-green-600 bg-clip-text text-transparent">
                      إضافة منتج من JSON
                    </h2>
                  </div>
                  <button
                    onClick={() => {
                      setShowJsonModal(false);
                      setJsonInput('');
                    }}
                    className="text-gray-400 hover:text-gray-600 p-3 rounded-xl hover:bg-gray-100 transition-colors duration-200"
                  >
                    <i className="ri-close-line text-xl"></i>
                  </button>
                </div>

                <div className="space-y-6">
                  {/* تعليمات الاستخدام */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-blue-800 mb-3">تعليمات الاستخدام:</h3>
                    <div className="text-sm text-blue-700 space-y-2">
                      <p>• الصق بيانات JSON للمنتج في المنطقة أدناه</p>
                      <p>• الحقول المطلوبة: title, titleAr, description, descriptionAr, price, categoryId, subcategoryId</p>
                      <p>• الحقول الاختيارية: id, images, originalPrice, available, features, featuresAr, specifications, isActive, isFeatured</p>
                      <p>• سيتم تجاهل الصور في هذا الإصدار ويمكن إضافتها لاحقاً</p>
                    </div>
                  </div>

                  {/* مثال JSON */}
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">أمثلة على تنسيق JSON:</h3>

                    {/* مثال منتج واحد */}
                    <div className="mb-4">
                      <h4 className="text-md font-medium text-gray-700 mb-2">1. إضافة منتج واحد:</h4>
                      <pre className="text-sm text-gray-700 bg-white p-3 rounded border overflow-x-auto">
{`{
  "id": "PROD-001",
  "title": "Product Name",
  "titleAr": "اسم المنتج",
  "description": "Product description",
  "descriptionAr": "وصف المنتج",
  "price": 100.50,
  "originalPrice": 150.00,
  "available": true,
  "categoryId": "category-id",
  "subcategoryId": "subcategory-id",
  "features": ["Feature 1", "Feature 2"],
  "featuresAr": ["ميزة 1", "ميزة 2"],
  "specifications": [
    {
      "nameEn": "Material",
      "nameAr": "المادة",
      "valueEn": "Stainless Steel",
      "valueAr": "فولاذ مقاوم للصدأ"
    }
  ],
  "isActive": true,
  "isFeatured": false
}`}
                      </pre>
                    </div>

                    {/* مثال عدة منتجات */}
                    <div>
                      <h4 className="text-md font-medium text-gray-700 mb-2">2. إضافة عدة منتجات (مصفوفة):</h4>
                      <pre className="text-sm text-gray-700 bg-white p-3 rounded border overflow-x-auto">
{`[
  {
    "id": "PROD-001",
    "title": "Product 1",
    "titleAr": "المنتج الأول",
    "description": "Description 1",
    "descriptionAr": "وصف المنتج الأول",
    "price": 100.50,
    "categoryId": "category-id",
    "subcategoryId": "subcategory-id",
    "features": ["Feature 1"],
    "featuresAr": ["ميزة 1"],
    "isActive": true
  },
  {
    "id": "PROD-002",
    "title": "Product 2",
    "titleAr": "المنتج الثاني",
    "description": "Description 2",
    "descriptionAr": "وصف المنتج الثاني",
    "price": 200.75,
    "categoryId": "category-id",
    "subcategoryId": "subcategory-id",
    "features": ["Feature 2"],
    "featuresAr": ["ميزة 2"],
    "isActive": true
  }
]`}
                      </pre>
                    </div>
                  </div>

                  {/* منطقة إدخال JSON */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      بيانات JSON للمنتج أو المنتجات *
                    </label>
                    <p className="text-sm text-gray-600 mb-2">
                      يمكنك إضافة منتج واحد (كائن JSON) أو عدة منتجات (مصفوفة من كائنات JSON)
                    </p>
                    <textarea
                      value={jsonInput}
                      onChange={(e) => setJsonInput(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 font-mono text-sm"
                      rows={15}
                      placeholder="الصق بيانات JSON هنا... (منتج واحد أو مصفوفة من المنتجات)"
                      dir="ltr"
                    />
                  </div>

                  {/* أزرار التحكم */}
                  <div className="flex space-x-4 space-x-reverse pt-6 border-t border-gray-200">
                    <button
                      onClick={handleJsonSubmit}
                      disabled={!jsonInput.trim()}
                      className="flex-1 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 disabled:from-gray-300 disabled:to-gray-400 text-white py-4 px-6 rounded-xl font-bold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl disabled:transform-none disabled:shadow-none"
                    >
                      إضافة المنتج
                    </button>
                    <button
                      onClick={() => {
                        setShowJsonModal(false);
                        setJsonInput('');
                      }}
                      className="flex-1 bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 text-gray-800 py-4 px-6 rounded-xl font-bold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                    >
                      إلغاء
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </AdminLayout>
    </>
  );
};

export default ProductsAdmin;
