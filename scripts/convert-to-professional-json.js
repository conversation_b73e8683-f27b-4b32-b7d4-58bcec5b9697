#!/usr/bin/env node

const fs = require('fs');

// قراءة البيانات الخام
function loadRawData() {
  try {
    const rawData = JSON.parse(fs.readFileSync('raw-products-data.json', 'utf8'));
    console.log(`📖 تم تحميل ${rawData.length} عنصر من البيانات الخام`);
    return rawData;
  } catch (error) {
    console.error('❌ خطأ في قراءة البيانات الخام:', error.message);
    return null;
  }
}

// استخراج المنتجات الفعلية
function extractProducts(rawData) {
  const products = rawData.filter(item => {
    // البحث عن العناصر التي تحتوي على كود منتج
    return item['CODE'] &&
           item['CODE'].toString().includes('RH') &&
           item['DESCRIPTION'] &&
           item['DESCRIPTION'].toString().length > 10;
  });

  console.log(`🔍 تم استخراج ${products.length} منتج فعلي`);
  return products;
}

// تنظيف وتحسين النص
function cleanText(text) {
  if (!text) return '';
  return text.toString()
    .replace(/\r\n/g, ' ')
    .replace(/\n/g, ' ')
    .replace(/\r/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();
}

// استخراج المواصفات من النص
function extractSpecifications(description) {
  const specs = [];

  // استخراج المادة
  const material = extractMaterial(description);
  if (material) {
    specs.push({
      nameEn: "Material",
      nameAr: "المادة",
      valueEn: material === 'ميلامين' ? 'Melamine' : material,
      valueAr: material
    });
  }

  // استخراج الحجم
  const size = extractSize(description);
  if (size) {
    specs.push({
      nameEn: "Size",
      nameAr: "الحجم",
      valueEn: size.replace('بوصة', 'inches').replace('سم', 'cm'),
      valueAr: size
    });
  }

  // استخراج اللون
  if (description.toLowerCase().includes('black')) {
    const color = description.toLowerCase().includes('matte') ? 'Matte Black' : 'Black';
    specs.push({
      nameEn: "Color",
      nameAr: "اللون",
      valueEn: color,
      valueAr: color === 'Matte Black' ? 'أسود مطفي' : 'أسود'
    });
  }

  // إضافة مواصفات إضافية
  specs.push({
    nameEn: "Usage",
    nameAr: "الاستخدام",
    valueEn: "Commercial Buffet",
    valueAr: "بوفيه تجاري"
  });

  return specs;
}



// إنشاء عنوان عربي احترافي
function generateArabicTitle(descriptionEn) {
  // استخراج المعلومات الأساسية
  const isBlack = descriptionEn.toLowerCase().includes('black');
  const isMatte = descriptionEn.toLowerCase().includes('matte');
  const size = extractSize(descriptionEn);
  const material = extractMaterial(descriptionEn);

  // تحديد نوع المنتج
  let productType = '';
  if (descriptionEn.toLowerCase().includes('serving bowl')) {
    productType = 'وعاء تقديم';
  } else if (descriptionEn.toLowerCase().includes('soup bowl')) {
    productType = 'وعاء شوربة';
  } else if (descriptionEn.toLowerCase().includes('salad bowl')) {
    productType = 'وعاء سلطة';
  } else if (descriptionEn.toLowerCase().includes('noodle bowl')) {
    productType = 'وعاء نودلز';
  } else if (descriptionEn.toLowerCase().includes('sauce bowl')) {
    productType = 'وعاء صلصة';
  } else if (descriptionEn.toLowerCase().includes('flat plate')) {
    productType = 'طبق مسطح';
  } else if (descriptionEn.toLowerCase().includes('deep plate')) {
    productType = 'طبق عميق';
  } else if (descriptionEn.toLowerCase().includes('round plate')) {
    productType = 'طبق دائري';
  } else if (descriptionEn.toLowerCase().includes('rectangle plate')) {
    productType = 'طبق مستطيل';
  } else if (descriptionEn.toLowerCase().includes('serving tray')) {
    productType = 'صينية تقديم';
  } else if (descriptionEn.toLowerCase().includes('tray')) {
    productType = 'صينية';
  } else if (descriptionEn.toLowerCase().includes('platter')) {
    productType = 'صينية تقديم';
  } else if (descriptionEn.toLowerCase().includes('mug')) {
    productType = 'كوب';
  } else {
    productType = 'منتج بوفيه';
  }

  // بناء العنوان
  let title = productType;

  if (material === 'ميلامين') {
    title += ' ميلامين';
  }

  if (isBlack && isMatte) {
    title += ' أسود مطفي';
  } else if (isBlack) {
    title += ' أسود';
  }

  if (size) {
    title += ` ${size}`;
  }

  return title.trim();
}

// استخراج الحجم
function extractSize(description) {
  const sizeMatch = description.match(/(\d+(?:\.\d+)?)\s*(inches?|cm|سم|بوصة)/i);
  if (sizeMatch) {
    const value = sizeMatch[1];
    const unit = sizeMatch[2].toLowerCase();
    if (unit.includes('inch') || unit.includes('بوصة')) {
      return `${value} بوصة`;
    } else {
      return `${value} سم`;
    }
  }
  return '';
}

// استخراج المادة
function extractMaterial(description) {
  if (description.toLowerCase().includes('melamine')) {
    return 'ميلامين';
  } else if (description.toLowerCase().includes('stainless steel')) {
    return 'فولاذ مقاوم للصدأ';
  } else if (description.toLowerCase().includes('ceramic')) {
    return 'سيراميك';
  } else if (description.toLowerCase().includes('porcelain')) {
    return 'بورسلين';
  }
  return '';
}

// إنشاء وصف عربي احترافي
function generateArabicDescription(titleEn, descriptionEn) {
  const baseDesc = `${generateArabicTitle(titleEn, descriptionEn)} عالي الجودة مصنوع من مواد متينة ومقاومة للكسر. مثالي للاستخدام في البوفيهات والمطاعم والفنادق. سهل التنظيف والصيانة مع تصميم أنيق وعملي.`;

  // إضافة تفاصيل خاصة بناءً على المواد
  if (descriptionEn.toLowerCase().includes('melamine')) {
    return baseDesc + ' مصنوع من الميلامين عالي الجودة المقاوم للصدمات والخدوش.';
  }

  if (descriptionEn.toLowerCase().includes('stainless steel')) {
    return baseDesc + ' مصنوع من الفولاذ المقاوم للصدأ عالي الجودة.';
  }

  return baseDesc;
}

// إنشاء المميزات
function generateFeatures(description, isArabic = false) {
  const baseFeatures = isArabic ? [
    'مواد عالية الجودة',
    'تصميم احترافي للبوفيه',
    'مقاوم للكسر والخدش',
    'سهل التنظيف والصيانة',
    'مناسب للاستخدام التجاري',
    'آمن للطعام'
  ] : [
    'High quality materials',
    'Professional buffet design',
    'Break and scratch resistant',
    'Easy to clean and maintain',
    'Commercial grade quality',
    'Food safe'
  ];
  
  // إضافة مميزات خاصة بناءً على الوصف
  const additionalFeatures = [];
  
  if (description.toLowerCase().includes('melamine')) {
    additionalFeatures.push(isArabic ? 'مصنوع من الميلامين المتين' : 'Made from durable melamine');
  }
  
  if (description.toLowerCase().includes('matte')) {
    additionalFeatures.push(isArabic ? 'تشطيب مطفي أنيق' : 'Elegant matte finish');
  }
  
  if (description.toLowerCase().includes('bowl')) {
    additionalFeatures.push(isArabic ? 'تصميم مثالي للسلطات والشوربات' : 'Perfect design for salads and soups');
  }
  
  return [...baseFeatures, ...additionalFeatures].slice(0, 6);
}

// تحويل منتج واحد إلى JSON احترافي
function convertProduct(rawProduct, index) {
  const code = rawProduct['CODE'].toString().trim();
  const descriptionEn = cleanText(rawProduct['DESCRIPTION'] || '');
  const price = parseFloat(rawProduct['UNIT PRICE']) || 0;
  
  // استخراج العنوان من الوصف
  let titleEn = descriptionEn.split('\n')[0]
    .replace(/drop hajer buffet/gi, '')
    .replace(/from drop hajer/gi, '')
    .replace(/Drop hajer/gi, '')
    .trim();

  // إنشاء عنوان عربي احترافي
  const titleAr = generateArabicTitle(descriptionEn);

  // إنشاء وصف عربي احترافي
  const descriptionArGenerated = generateArabicDescription(titleEn, descriptionEn);

  // تحديد السعر مع هامش ربح
  const finalPrice = Math.round(price * 1.15); // زيادة 15%
  const originalPrice = Math.round(price * 1.35); // زيادة 35% للسعر الأصلي
  
  return {
    id: code.replace(/\s+/g, ''),
    title: titleEn.length > 60 ? titleEn.substring(0, 57) + '...' : titleEn,
    titleAr: titleAr.length > 60 ? titleAr.substring(0, 57) + '...' : titleAr,
    description: descriptionEn.length > 200 ? descriptionEn.substring(0, 197) + '...' : descriptionEn,
    descriptionAr: descriptionArGenerated.length > 200 ? descriptionArGenerated.substring(0, 197) + '...' : descriptionArGenerated,
    price: finalPrice,
    originalPrice: originalPrice,
    available: true,
    categoryId: "Buffetware",
    subcategoryId: "Melamine-Salad", // معظم المنتجات ميلامين
    features: generateFeatures(descriptionEn, false),
    featuresAr: generateFeatures(descriptionArGenerated, true),
    specifications: extractSpecifications(descriptionEn, false),
    isActive: true,
    isFeatured: index < 10 // أول 10 منتجات مميزة
  };
}

// الدالة الرئيسية
function main() {
  console.log('🚀 بدء تحويل البيانات إلى JSON احترافي...\n');
  
  // تحميل البيانات
  const rawData = loadRawData();
  if (!rawData) return;
  
  // استخراج المنتجات
  const products = extractProducts(rawData);
  if (products.length === 0) {
    console.error('❌ لم يتم العثور على منتجات صالحة');
    return;
  }
  
  // تحويل المنتجات
  console.log('🔄 تحويل المنتجات...');
  const convertedProducts = products.map((product, index) => {
    try {
      return convertProduct(product, index);
    } catch (error) {
      console.error(`❌ خطأ في تحويل المنتج ${index + 1}:`, error.message);
      return null;
    }
  }).filter(Boolean);
  
  console.log(`✅ تم تحويل ${convertedProducts.length} منتج بنجاح`);
  
  // حفظ النتيجة
  const outputPath = 'professional-products.json';
  fs.writeFileSync(outputPath, JSON.stringify(convertedProducts, null, 2), 'utf8');
  
  console.log(`💾 تم حفظ المنتجات في: ${outputPath}`);
  console.log(`📊 إحصائيات:`);
  console.log(`   - عدد المنتجات: ${convertedProducts.length}`);
  console.log(`   - المنتجات المميزة: ${convertedProducts.filter(p => p.isFeatured).length}`);
  console.log(`   - متوسط السعر: ${Math.round(convertedProducts.reduce((sum, p) => sum + p.price, 0) / convertedProducts.length)} ريال`);
  
  // عرض عينة
  console.log('\n🔍 عينة من المنتجات المحولة:');
  console.log('=' .repeat(80));
  convertedProducts.slice(0, 2).forEach((product, index) => {
    console.log(`\n📦 المنتج ${index + 1}:`);
    console.log(`   ID: ${product.id}`);
    console.log(`   العنوان: ${product.titleAr}`);
    console.log(`   السعر: ${product.price} ريال`);
    console.log(`   المميزات: ${product.featuresAr.slice(0, 2).join(', ')}...`);
  });
}

// تشغيل السكربت
if (require.main === module) {
  main();
}

module.exports = { convertProduct, extractProducts };
